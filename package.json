{"name": "jiakaobaodian-66-mona", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "devHost": "npm run dev -- --host", "build": "vue-tsc -b && vite build", "build:test": "vue-tsc -b && vite build --mode development", "preview": "vite preview"}, "dependencies": {"@jiakaobaodian/jiakaobaodian-utils": "1.1.9", "@simplex/simple-base": "7.4.11", "@simplex/simple-core": "4.0.22", "@simplex/simple-mcprotocol": "3.7.0", "@simplex/simple-mock": "1.0.1", "@simplex/simple-oort": "4.3.2", "@simplex/simple-pay": "1.0.4", "@simplex/simple-secure-upload": "3.6.2", "ant-design-vue": "^4.2.6", "html2canvas": "^1.4.1", "less": "^4.2.0", "less-loader": "^12.2.0", "lodash": "^4.17.21", "pinia": "2.3.0", "pinia-plugin-persistedstate": "4.2.0", "qrcode": "^1.5.4", "rollup-plugin-dynamic-import-variables": "^1.1.0", "swiper": "^11.2.2", "vconsole": "^3.15.1", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@types/lodash": "^4.17.13", "@types/node": "^22.10.2", "@vitejs/plugin-legacy": "^6.0.0", "@vitejs/plugin-vue": "^5.0.5", "postcss-pxtorem": "^6.1.0", "rollup": "4.21.3", "typescript": "^5.2.2", "vite": "^6.0.7", "vue-tsc": "2.0.21"}}