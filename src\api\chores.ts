import { CarType, KemuType, URLParams } from "@/common/env";
import { request } from "@/common/request";
import { once } from "lodash";

export async function getServerTime() {
    const res = await request({
        hostName: 'squirrel',
        url: 'api/web/time/now.htm'
    });
    return res.nowMillis as number;
}

/** 获取远程配置 */
export const getConfig = once((): Promise<Record<string, any>> => {
    return request({
        hostName: 'config',
        url: 'api/web/v4/config/get.htm'
    });
});

/** 获取swallow远程配置,已购买页获取远程配置的kemu是写死的，所以需要传递参数kemu */
export function getSwallowConfig(data: { key: string }) {
    return request({
        hostName: 'swallow',
        url: 'api/web/config/get-config.htm',
        data: {
            carType: URLParams.carStyle || CarType.CAR,
            kemu: URLParams.kemuStyle || KemuType.Ke1,
            ...data
        }
    });
}