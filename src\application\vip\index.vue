<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { defaultImg } from '@/common/const';
import { Platform, URLCommon } from '@/common/env';
import { makeToast, dateFormat } from '@/common/utils';
import { getAuthToken, webClose } from '@/common/core';
import { MCProtocol, MCShare } from '@simplex/simple-base'

// 导入图片资源
import backIcon from './images/1f8747.png';
import giftIcon from './images/5aacd0.png';
import videoEffectIcon from './images/e85009.png';
import quickGenIcon from './images/872fd9.png';
import oneClickIcon from './images/52d36a.png';
import hdQualityIcon from './images/af60c1.png';

// 订阅选项数据
const subscriptionOptions = ref([
  {
    id: 'week',
    title: '周订阅',
    price: '28',
    originalPrice: '78',
    dailyPrice: '4元/天',
    isPopular: true
  },
  {
    id: 'quarter',
    title: '季订阅',
    price: '158',
    originalPrice: '288',
    dailyPrice: '1.75元/天',
    isPopular: false
  },
  {
    id: 'year',
    title: '年订阅',
    price: '198',
    originalPrice: '598',
    dailyPrice: '0.7元/天',
    isPopular: false
  }
]);

// 会员特权数据
const memberBenefits = ref([
  { icon: videoEffectIcon, title: '视频特效' },
  { icon: quickGenIcon, title: '快速生成' },
  { icon: oneClickIcon, title: '一键同款' },
  { icon: hdQualityIcon, title: '高清画质' }
]);

// 选中的订阅选项
const selectedOption = ref('week');

// 会员状态
const isVipMember = ref(false);
const memberInfo = ref({
  purchaseTime: '2025-02-01 12:20',
  expireTime: '2025-06-12',
  giftAmount: '300'
});

// 选择订阅选项
const selectOption = (optionId: string) => {
  selectedOption.value = optionId;
};

// 获取选中选项的价格
const selectedPrice = computed(() => {
  const option = subscriptionOptions.value.find(opt => opt.id === selectedOption.value);
  return option?.price || '28';
});

// 处理购买
const handlePurchase = async () => {
  // 这里应该调用实际的购买接口
  // 模拟购买成功
  isVipMember.value = true;
  makeToast('购买成功！');
};

// 处理去使用
const handleGoUse = () => {
  // 跳转到使用页面或关闭当前页面
  webClose();
};

onMounted(async () => {
  // 检查会员状态
  // 这里应该调用实际的会员状态检查接口
  // isVipMember.value = await checkVipStatus();
});
</script>

<template>
  <div class="vip-page">
    <!-- 背景图片 -->
    <div class="background-image"></div>
    
    <!-- 可滚动内容区域 -->
    <div class="scrollable-content">
      <!-- 顶部区域 -->
      <div class="header-section">
        <img :src="backIcon" alt="返回" class="back-button" />
        
        <!-- 渐变遮罩 -->
        <div class="gradient-overlay"></div>
        
        <!-- 标题区域 -->
        <div class="title-section">
          <h1 class="main-title">轻松开启动态创作之旅</h1>
          <p class="subtitle">视频特效</p>
        </div>
      </div>
      
      <!-- 会员标题 -->
      <div class="member-title-section">
        <h2 class="member-title">{{ isVipMember ? '已成为会员' : '成为会员' }}</h2>
        <p v-if="!isVipMember" class="member-subtitle">海量素材 超多功能快尝试</p>
        <div v-if="isVipMember" class="member-info">
          <p class="member-purchase-time">{{ memberInfo.purchaseTime }} 赠送{{ memberInfo.giftAmount }}<img :src="giftIcon" alt="赠送" class="gift-icon" /></p>
          <p class="member-expire-time">{{ memberInfo.expireTime }}到期，赠送的<img :src="giftIcon" alt="赠送" class="gift-icon" />未使用将过期;</p>
        </div>
      </div>
      
      <!-- 订阅选项 - 仅购买前显示 -->
      <div v-if="!isVipMember" class="subscription-section">
        <div class="subscription-options">
          <div 
            v-for="option in subscriptionOptions" 
            :key="option.id"
            class="subscription-option"
            :class="{
              'selected': selectedOption === option.id,
              'popular': option.isPopular
            }"
            @click="selectOption(option.id)"
          >
            <div class="option-title">{{ option.title }}</div>
            <div class="option-price">
              <span class="currency">¥ </span>
              <span class="amount">{{ option.price }}</span>
            </div>
            <div class="original-price">¥{{ option.originalPrice }}</div>
            <div class="daily-price">{{ option.dailyPrice }}</div>
            <div v-if="option.isPopular" class="popular-badge">限时体验</div>
          </div>
        </div>
        
        <!-- 赠送说明 -->
        <div class="gift-info">
          <span class="gift-text">赠送300</span>
          <img :src="giftIcon" alt="赠送" class="gift-icon" />
          <span class="gift-desc">，到期后按28/周自动续费，可随时取消</span>
        </div>
      </div>
      
      <!-- 会员特权 -->
      <div class="benefits-section">
        <h3 class="benefits-title">尊享以下会员特权</h3>
        <div class="benefits-grid">
          <div 
            v-for="benefit in memberBenefits" 
            :key="benefit.title"
            class="benefit-item"
          >
            <img :src="benefit.icon" :alt="benefit.title" class="benefit-icon" />
            <span class="benefit-title">{{ benefit.title }}</span>
          </div>
        </div>
      </div>
      
      <!-- 底部间距，为悬浮区域留出空间 -->
      <div class="bottom-spacer"></div>
    </div>
    
    <!-- 悬浮底部区域 -->
    <div class="floating-bottom">
      <!-- 底部渐变遮罩 -->
      <div class="bottom-gradient"></div>
      
      <!-- 购买按钮 -->
      <div class="purchase-section">
        <template v-if="!isVipMember">
          <div class="purchase-button" @click="handlePurchase">
            <div class="button-glow"></div>
            <span class="price-text">¥{{ selectedPrice }}</span>
            <span class="purchase-text">立即购买</span>
          </div>
          <div class="agreement-section">
            <div class="checkbox"></div>
            <span class="agreement-text">开通前请阅读《会员协议》(含自动续贸条款) </span>
            <span class="restore-text">恢复购买></span>
          </div>
        </template>
        
        <div v-else class="use-button" @click="handleGoUse">
          <span class="use-text">去使用</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.vip-page {
  height: 100vh;
  background: #0a0a0a;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  width: 375px;
  margin: 0 auto;
}

.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 375px;
  height: 296px;
  z-index: 1;
}

.scrollable-content {
  flex: 1;
  overflow-y: auto;
  position: relative;
  z-index: 2;
}

.header-section {
  position: relative;
  height: 296px;
  z-index: 2;
}

.back-button {
  position: absolute;
  top: 54px;
  left: 15px;
  width: 25px;
  height: 25px;
  z-index: 3;
}

.gradient-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 130px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  z-index: 2;
}

.title-section {
  position: absolute;
  bottom: 60px;
  left: 28px;
  z-index: 3;
}

.main-title {
  color: white;
  font-size: 25px;
  font-weight: bold;
  line-height: 35px;
  margin: 0 0 5px 0;
}

.subtitle {
  color: rgba(255, 255, 255, 0.76);
  font-size: 15px;
  line-height: 21px;
  margin: 0;
}

.member-title-section {
  text-align: center;
  margin: 20px 0;
  z-index: 2;
  position: relative;
}

.member-title {
  color: white;
  font-size: 25px;
  font-weight: bold;
  line-height: 36px;
  margin: 0 0 5px 0;
}

.member-subtitle {
  color: #6d6d6d;
  font-size: 15px;
  font-weight: bold;
  line-height: 21px;
  margin: 0;
}

.member-info {
  margin-top: 10px;
}

.member-purchase-time {
  color: white;
  font-size: 12px;
  line-height: 17px;
}

.member-expire-time {
  color: white;
  font-size: 12px;
  line-height: 17px;
}

.subscription-section {
  padding: 0 19px;
  z-index: 2;
  position: relative;
}

.subscription-options {
  display: flex;
  gap: 11px;
  margin-bottom: 20px;
}

.subscription-option {
  flex: 1;
  height: 125px;
  border-radius: 22px;
  padding: 15px 10px;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  background: hsla(230, 11%, 11%, 1);
  
  &.selected {
    transform: scale(1.05);
  }
  
  &.popular {
    background: linear-gradient(135deg, hsla(196, 100%, 27%, 1) 0%, hsla(217, 65%, 22%, 1) 41%, hsla(231, 19%, 14%, 1) 100%);
    border: 2px solid hsla(184, 100%, 54%, 1);
  }
}

.option-title {
  color: white;
  font-size: 14px;
  font-weight: bold;
  line-height: 20px;
  text-align: center;
  margin-bottom: 10px;
}

.option-price {
  text-align: center;
  margin-bottom: 5px;
}

.currency {
  color: white;
  font-size: 16px;
  font-weight: bold;
}

.amount {
  color: white;
  font-size: 26px;
  font-weight: bold;
}

.original-price {
  color: rgba(255, 255, 255, 0.35);
  font-size: 11px;
  line-height: 16px;
  text-decoration: line-through;
  text-align: center;
  margin-bottom: 5px;
}

.daily-price {
  color: rgba(255, 255, 255, 0.35);
  font-size: 11px;
  line-height: 16px;
  text-align: center;
  margin-bottom: 10px;
}

.popular-badge {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 50%, #a855f7 86%, #a855f7 100%);
  color: white;
  font-size: 14px;
  line-height: 20px;
  padding: 5px 10px;
  border-radius: 15px;
  white-space: nowrap;
}

.gift-info {
  margin-bottom: 30px;
  text-align: center;
}

.gift-icon {
  width: 20px;
  height: 20px;
  vertical-align: bottom;
}

.gift-text {
  color: white;
  font-size: 12px;
  line-height: 17px;
}

.gift-desc {
  color: white;
  font-size: 12px;
  line-height: 17px;
}

.benefits-section {
  text-align: center;
  margin-bottom: 30px;
  z-index: 2;
  position: relative;
}

.benefits-title {
  color: white;
  font-size: 20px;
  font-weight: bold;
  line-height: 28px;
  margin: 0 0 25px 0;
}

.benefits-grid {
  display: flex;
  justify-content: space-around;
  padding: 0 42px;
}

.benefit-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
}

.benefit-icon {
  width: 45px;
  height: 45px;
}

.benefit-title {
  color: white;
  font-size: 12px;
  font-weight: bold;
  line-height: 17px;
}

.bottom-spacer {
  height: 20px;
}

.floating-bottom {
  position: relative;
  z-index: 10;
}

.bottom-gradient {
  position: absolute;
  top: -20px;
  left: 0;
  width: 100%;
  height: 130px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.93) 100%);
  z-index: 1;
}

.purchase-section {
  position: relative;
  padding: 0 16px;
  z-index: 2;
}

.purchase-button {
  position: relative;
  background: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);
  border-radius: 10px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  box-shadow: 0px 4px 12px -4px rgba(20, 20, 20, 0.5);
  margin-bottom: 15px;
}

.button-glow {
  position: absolute;
  left: 87px;
  top: 0;
  width: 108px;
  height: 50px;
  background: rgba(34, 211, 238, 0.6);
  border-radius: 50%;
  filter: blur(23px);
}

.price-text {
  color: white;
  font-size: 17px;
  line-height: 24px;
}

.purchase-text {
  color: white;
  font-size: 17px;
  line-height: 24px;
}

.use-button {
  background: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);
  border-radius: 10px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0px 4px 12px -4px rgba(20, 20, 20, 0.5);
  margin-bottom: 15px;
  cursor: pointer;
}

.use-text {
  color: white;
  font-size: 17px;
  font-weight: bold;
  line-height: 24px;
}

.agreement-section {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 29px;
  margin-bottom: 15px;
}

.checkbox {
  width: 15px;
  height: 15px;
  border: 2px solid #666;
  border-radius: 50%;
  flex-shrink: 0;
}

.agreement-text {
  color: #a1a1a1;
  font-size: 12px;
  line-height: 17px;
  flex: 1;
}

.restore-text {
  color: #6366f1;
  font-size: 12px;
  line-height: 17px;
}
</style>
