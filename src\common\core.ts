/*
 * ------------------------------------------------------------------
 * 核心功能
 * ------------------------------------------------------------------
 */

import { Platform, URLParams } from './env';
import { MCProtocol } from '@simplex/simple-base';

/** 获取用户的登录token */
export function getAuthToken(): Promise<string> {
    return new Promise((resolve) => {
        let authToken = URLParams.authToken;

        if (!Platform.isMuCang) {
            authToken = authToken || localStorage.getItem('vip-active-authToken') || ''
        }

        if (authToken && authToken !== 'undefined') {
            resolve(authToken);
        } else if (Platform.isMuCang) {
            MCProtocol.Core.User.get(function (data: any) {
                if (data.success.toString() === 'true') {
                    authToken = data.data.authToken;
                } else {
                    authToken = '';
                }
                resolve(authToken as string);
            });
        } else {
            resolve('');
        }
    });
}

export interface UserInfo {
    avatar: string;
    nickname: string;
    phone: string;
}

export async function getUserInfo(): Promise<UserInfo | null> {
    const authToken = await getAuthToken();
    if (authToken) {
        if (!Platform.isMuCang) {
            return JSON.parse(localStorage.getItem('mucang_userInfo') || '{}');
        } else {
            const data = await new Promise<any>(resolve => MCProtocol.Core.User.get(resolve));
            // console.log('userInfo', data.data);
            return data.data;
        }
    }
    return null;
}

export function goBack() {
    if (Platform.isMuCang) {
        MCProtocol.Core.Web.back();
    } else {
        history.back();
    }
}

export function webClose() {
    MCProtocol.Core.Web.close();
}

export function saveCache(obj: { key: string, value: string }) {
    if (Platform.isMuCang) {
        MCProtocol.Core.System.setcache({
            key: obj.key,
            value: obj.value
        });
    } else {
        window.localStorage.setItem(obj.key, obj.value);
    }
}

export function getCache(key: string): Promise<string> {
    return new Promise((resolve) => {
        if (Platform.isMuCang) {
            MCProtocol.Core.System.getcache({
                key: key,
                callback: function (data: any) {
                    resolve(data || '');
                }
            });
        } else {
            resolve(window.localStorage.getItem(key) || '');
        }
    });
}

/** 获取系统信息 */
export function getSystemInfo(): Promise<Record<string, string>> {
    return new Promise((resolve) => {
        if (Platform.isMuCang) {
            MCProtocol.Core.System.info((baseParams) => {
                if (baseParams.data) {
                    if (typeof baseParams.data === 'string') {
                        baseParams = JSON.parse(baseParams.data);
                    } else {
                        baseParams = baseParams.data;
                    }
                } else if (typeof baseParams === 'string') {
                    baseParams = JSON.parse(baseParams);
                }
                resolve(baseParams);
            });
        } else {
            resolve(URLParams);
        }
    });
}