import { Component, h, render, defineAsyncComponent } from "vue";
import readProtocolStore from '@/store/modules/readProtocol';

// 弹窗
export function dialog(vNode: Component, config?: any, children?: any) {
    const container = document.createElement('div') as unknown as Element;

    function dialogClose() {
        document.body.removeChild(container)
    }

    return new Promise(resovle => {
        function OK(flag: boolean) {
            dialogClose();
            resovle(flag)
        }
        function close(flag: boolean) {
            dialogClose();
            if (typeof flag === 'boolean') {
                resovle(flag)
            }
        }
        const parentVNode = h(vNode, {
            onOk: OK,
            onClose: close,
            ...config
        }, children)
        render(parentVNode, container);
        document.body.appendChild(container)
    })
}

export function sureDialog(config: any){
    const Dialog = defineAsyncComponent(() => import('../components/Dialog/index.vue'));

    return dialog(Dialog, {
        ...config
    })
}

// 检查是否选中协议
export function checkReadedDialog(protocolConfig?: any): Promise<void> {
    const Dialog = defineAsyncComponent(() => import('../components/Dialog/index.vue'));
    const ReadProtocol = defineAsyncComponent(() => import('../components/ReadProtocol/index.vue'));

    const readProtocolInfo = readProtocolStore();

    function renderReadProtocol() {
        return h(ReadProtocol, {
            hideCheckbox: true,
            ...protocolConfig
        })
    }

    return new Promise(resolve => {
        if (!readProtocolInfo.hasReadInfo) {
            dialog(Dialog, {
                cancleText: '不同意'
            }, renderReadProtocol).then(flag => {
                if (flag) {
                    readProtocolInfo.setHasRead(true);
                    resolve();
                }
            });
        } else {
            resolve();
        }
    })
}

// 打开选择支付弹窗
export function openPayDialog(config?: any){
    const PayDialog = defineAsyncComponent(() => import('../components/PayDialog/index.vue'));

    return dialog(PayDialog, {
        readProtocolTheme: 1,
        ...config
    })

}

// 购买完成倒计时弹窗
export function loadingTimes() {
    const LoadingTimes = defineAsyncComponent(() => import('../components/LoadingTimes/index.vue'));

    return dialog(LoadingTimes)

}

// 优惠券动画
export function couponAnimation(params: {
    zIndex?: number,
    // 优惠券动画目标选择器
    couponTargetDomSelect: string
    // 优惠券数据
    couponData: any
    // 商品数据
    goodsData: any
}) {
    const { zIndex, couponTargetDomSelect, goodsData, couponData } = params;
    console.log(couponData, 11111111111)
    const $mask = document.createElement('div');
    const $body = document.querySelector('body');

    $mask.style.position = 'fixed';
    $mask.style.zIndex = zIndex ? String(zIndex) : '1001';
    $mask.style.top = '0';
    $mask.style.bottom = '0';
    $mask.style.left = '0';
    $mask.style.right = '0';
    $mask.style.background = 'rgba(0, 0, 0, 0.3)';
    $mask.setAttribute('skip', 'true');

    $body!.append($mask);

    const targetDom = document.querySelector(couponTargetDomSelect);

    const domRect = targetDom && targetDom.getBoundingClientRect();

    const $animate = document.createElement('div');
    $animate.style.position = 'absolute';
    $animate.style.width = '200px';
    $animate.style.height = '200px';
    $animate.style.top = domRect?.top ? domRect.top + (domRect.height / 2) + 'px' : '50%';
    $animate.style.left = domRect?.left ? domRect.left + (domRect.width / 2) + 'px' : '50%';
    $animate.style.transform = 'translate(-50%, -50%) scale(0)';

    const $priceBox = document.createElement('div');
    const $unit = document.createElement('span');
    const $price = document.createElement('span');

    $priceBox.style.position = 'absolute';
    $priceBox.style.bottom = '58px';
    $priceBox.style.left = '44px';
    $priceBox.style.width = '90px';
    $priceBox.style.textAlign = 'center';
    $priceBox.style.color = 'white';

    $unit.textContent = '￥';

    if (+goodsData?.payPrice < +couponData.price) {
        $price.textContent = Math.ceil(+goodsData.payPrice) + '';
    } else {
        $price.textContent = couponData.price;
    }

    $animate.style.background = 'url(http://exam-room.mc-cdn.cn/exam-room/2023/09/20/16/08b286b0a4ef4a8bbe33ef22aaae84c7.png) no-repeat center center/cover';

    if (Number($price.textContent) > 99) {
        $unit.style.fontSize = '20px';
        $price.style.fontSize = '40px';
    } else {
        $unit.style.fontSize = '28px';
        $price.style.fontSize = '52px';
    }

    $animate.style.animation = 'couponAnimate 1.2s';

    $animate.addEventListener('animationend', () => {
        $mask.remove();
    });

    $priceBox.append($unit);
    $priceBox.append($price);
    $animate.append($priceBox);
    $mask.append($animate);
}