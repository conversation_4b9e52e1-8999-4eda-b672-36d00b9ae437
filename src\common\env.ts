/*
 * ------------------------------------------------------------------
 * 环境上下文相关
 * ------------------------------------------------------------------
 */

const userAgent = navigator.userAgent.toLowerCase();

/** 车型 */
export enum CarType {
    CAR = 'car',
    TRUCK = 'truck',
    BUS = 'bus',
    MOTO = 'moto',
    GUACHE = 'light_trailer',
    // 资格证
    KEYUN = 'keyun',
    HUOYUN = 'huoyun',
    WEIXIAN = 'weixian',
    JIAOLIAN = 'jiaolian',
    CHUZU = 'chuzu',
    WANGYUE = 'wangyue',
    WEIXIAN_YAYUN = 'weixian_yayun',
    CHACHE = 'chache',
    WEIXIAN_ZHUANGXIE = 'weixian_zhuangxie',
    BAOZHA = 'baozha',
    BAOZHA_YAYUN = 'baozha_yayun',
    BAOZHA_ZHUANGXIE = 'baozha_zhuangxie',
    JIAOLIAN_ZAIJIAOYU = 'jiaolian_zaijiaoyu',
    // 无人机相关（资格证的一种）
    MULTI_WVR = 'multi_wvr',
    MULTI_BVR = 'multi_bvr',
    MULTI_COACH = 'multi_coach',
    FIXED_WVR = 'fixed_wvr',
    FIXED_BVR = 'fixed_bvr',
    FIXED_COACH = 'fixed_coach',
    HELICOPTER_WVR = 'helicopter_wvr',
    HELICOPTER_BVR = 'helicopter_bvr',
    HELICOPTER_COACH = 'helicopter_coach',
    VTOL_WVR = 'vtol_wvr',
    VTOL_BVR = 'vtol_bvr',
    VTOL_COACH = 'vtol_coach'
}

/** 科目 */
export enum KemuType {
    Ke0 = 0,
    Ke1 = 1,
    Ke2 = 2,
    Ke3 = 3,
    Ke4 = 4
}

/** url参数，统一从这里取 */
export const URLParams = (function () {
    return window.location.search.substring(1).split('&').reduce<Record<string, string>>((obj, segment) => {
        if (!segment) {
            return obj;
        }
        const [key, value] = segment.split('=');
        // iOS需要decode两遍
        obj[decodeURIComponent(key)] = decodeURIComponent(decodeURIComponent(value));
        return obj;
    }, {});
})();

const wurenjiArr = [CarType.MULTI_WVR, CarType.MULTI_BVR, CarType.MULTI_COACH, CarType.FIXED_WVR, CarType.FIXED_BVR, CarType.FIXED_COACH, CarType.HELICOPTER_WVR, CarType.HELICOPTER_BVR, CarType.HELICOPTER_COACH, CarType.VTOL_WVR, CarType.VTOL_BVR, CarType.VTOL_COACH];

// eslint-disable-next-line max-len
const zigezhengArr = [CarType.KEYUN, CarType.HUOYUN, CarType.WEIXIAN, CarType.JIAOLIAN, CarType.CHUZU, CarType.WANGYUE, CarType.WEIXIAN_YAYUN, CarType.CHACHE, CarType.WEIXIAN_ZHUANGXIE, CarType.BAOZHA, CarType.BAOZHA_YAYUN, CarType.BAOZHA_ZHUANGXIE, CarType.JIAOLIAN_ZAIJIAOYU, ...wurenjiArr];



/** 通用URL参数及判断 */
export const URLCommon = {
    /** 题库，即车型 */
    tiku: (URLParams.carStyle || CarType.CAR) as CarType,
    /** 科目 */
    kemu: !URLParams.kemuStyle ? KemuType.Ke1 : (+URLParams.kemuStyle as KemuType),
    /** 是否资格证 */
    isZigezheng: zigezhengArr.indexOf(URLParams.carStyle as CarType) > -1,
    /** 满分学习场景 */
    isScore12: URLParams.sceneCode === '102' && URLParams.patternCode !== '102',
    /** 长辈版模式 */
    isElder: URLParams.patternCode === '102',
    /** 正常版模式 */
    isNormal: URLParams.sceneCode !== '102' && URLParams.patternCode !== '102',
    /** 是否是3d */
    is3D: URLParams._appName === 'jiakao3d'
};


/** 平台判断 */
export const Platform = {
    isWeiXin: !!userAgent.match(/MicroMessenger/i),
    isAliPay: !!userAgent.match(/AlipayClient/i),
    isAndroid: userAgent.indexOf('android') > -1,
    isHarmony: userAgent.indexOf('openharmony') > -1,
    isMuCang: userAgent.indexOf('mucang') > -1,
    isIOS: userAgent.indexOf('iphone') > -1 || userAgent.indexOf('ipad') > -1,
}

/** 支付方式 */
export enum PayType {
    Alipay = 1,
    Weixin = 2,
    ApplePay = 3,
    Harmony = 8
}
