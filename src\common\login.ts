/*
 * ------------------------------------------------------------------
 * 登录相关
 * ------------------------------------------------------------------
 */
import { getAuthToken, getUserInfo } from '@/common/core';
import { Platform } from '@/common/env';
import { promisify } from '@/common/utils';
import { jump } from '@jiakaobaodian/jiakaobaodian-utils';
import { MCProtocol } from '@simplex/simple-base';
import { ListenLogin, LoginType } from './broadcast/login';
import userStore from '@/store/modules/user';
import router from '@/router/index'

const Jump = jump.default;

export const login = async (reload = true) => {
    if (Platform.isMuCang) {
        const info = await promisify(MCProtocol.Core.User.login)({
            from: 'jiakaobaodian',
            skipAuthRealName: true,
            pageType: 'quicklogin'
        });

        if (info.success) {
            const userInfoStore = userStore();

            const userInfo = await getUserInfo();

            userInfoStore.setUserInfo(userInfo);
            
            if (reload) {
                Jump.reload();
                return '';
            }
            return await getAuthToken();
        }

        throw new Error('登录失败');

    } else {
        router.push({
            name: 'login'
        });

        await new Promise(resolve => {
            ListenLogin(({ type, data }: { type: string, data: string }) => {
                if (type === LoginType.success) {
                    resolve(data)
                }
            }, true)
        })
    }

    if (reload) {
        Jump.reload();
        return '';
    }

    return await getAuthToken();
};

export const checkedLogin = async (reload = true) => {
    const authToken = await getAuthToken();

    if (!authToken) {
        return await login(reload);
    }

    return authToken;
};