import { PayStatusEnum } from "@/api/pay";
import { PayType, Platform } from "./env";
import payTypeStore from '@/store/modules/payType';
import { noop } from "lodash";
import { Pay } from '@simplex/simple-pay';
import { jump } from "@jiakaobaodian/jiakaobaodian-utils";
const Jump = jump.default;

export const PayRef = 'JIAKAOBAODIAN';

const payChannels: Record<PayType, string> = {
    [PayType.Alipay]: 'alipay_mobile',
    [PayType.Weixin]: 'weixin_mobile',
    [PayType.ApplePay]: 'apple_mobile',
    [PayType.Harmony]: 'huawei_mobile'
};

interface OrderInfo { content: string, orderNo: string }

export async function muCangOrderPay(orderInfo: OrderInfo): Promise<{ type: PayStatusEnum, orderNo: string }> {
    const payTypeInfo = payTypeStore();
    const extraData = {
        vipType: 'vip_jk'
    };

    location.href = `http://pay.nav.mucang.cn/pay?payType=vip&content=${encodeURIComponent(orderInfo.content)}&orderNumber=${orderInfo.orderNo}&extraData=${JSON.stringify(extraData)}&payChannel=${payChannels[payTypeInfo.payType]}&unifyCallback=true&callback=`;

    return await new Promise<{ type: PayStatusEnum, orderNo: string }>((resolve, reject) => {
        window.buyCancel = () => {
            window.buyCancel = noop;
            reject(new Error('支付取消'));
        };
        window.buyFailed = () => {
            window.buyFailed = noop;
            reject(new Error('支付失败'));
        };
        window.buySuccess = () => {
            console.log('支付成功');
            window.buySuccess = noop;
            resolve({
                type: PayStatusEnum.PAY_SUCCESS,
                orderNo: orderInfo.orderNo
            });
        };
    });
}




export async function weixinMpOrderPay(orderInfo: OrderInfo, params: { redirectUrl: string }) {
    const { baseUrl, hashParamsStr, urlParams } = Jump.dealUrl(params.redirectUrl);
    // 非APP内下单跳转的地址
    const url = `${baseUrl}?${Jump.delRepeatUrlParams(
        {
            ...urlParams,
            orderNumber: orderInfo.orderNo
        },
        false
    )}${hashParamsStr}`;
    Pay.weixin.mp({
        content: orderInfo.content,
        callBack: function () {
            window.location.href = url;
        }
    });
}

export async function weixinH5OrderPay(orderInfo: OrderInfo, params: { redirectUrl: string }) {
    const { baseUrl, hashParamsStr, urlParams } = Jump.dealUrl(params.redirectUrl);
    // 非APP内下单跳转的地址
    const url = `${baseUrl}?${Jump.delRepeatUrlParams(
        {
            ...urlParams,
            orderNumber: orderInfo.orderNo
        },
        false
    )}${hashParamsStr}`;
    Pay.weixin.h5({
        content: orderInfo.content,
        redirectUrl: url
    });
}

export async function alipayOrderPay(orderInfo: OrderInfo) {
    Pay.alipay.h5({ content: orderInfo.content });
}

/**
 * 一般选择微信支付宝（拉卡拉，支付通不能用这个方法）等可以选择支付方式的就可以用这个方法
*/
export async function orderPay(orderInfo: any, params?: { openId: string, redirectUrl: string }) {
    const payTypeInfo = payTypeStore();
    // 只有木仓支付可以await， 别的支付都是直接跳转
    if (Platform.isMuCang) {
        return await muCangOrderPay(orderInfo);
    }
    if (payTypeInfo.payType === PayType.Weixin) {
        if (Platform.isWeiXin) {
            weixinMpOrderPay(orderInfo, params!);
        } else {
            weixinH5OrderPay(orderInfo, params!);
        }
    } else {
        alipayOrderPay(orderInfo);
    }

    return '';

}
