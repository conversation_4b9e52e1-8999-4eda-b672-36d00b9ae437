/*
 * ------------------------------------------------------------------
 * 网络请求
 * ------------------------------------------------------------------
 */

import { MCBaseStore } from '@simplex/simple-base';
import { makeToast, sign } from './utils';
import { Platform, URLParams } from './env';
import { jump } from '@jiakaobaodian/jiakaobaodian-utils';
import { getAuthToken } from './core';
const Jump = jump.default;

type HostName = 'sirius' | 'misc' | 'activity' | 'config' | 'swallow' | 'jiakao3d' | 'squirrel' | 'cheyouquan' | 'monkey' | 'jiakao' | 'tiku' | 'panda' | 'pony' | 'parrot' | 'rights' | 'login' | 'apiShare' | 'mona';

interface RequestOptions {
    hostName?: HostName,
    url: string,
    method?: 'GET' | 'POST',
    headers?: Record<string, any>,
    data?: Record<string, any>
    others?: any
    noToast?: boolean
    returnStore?: boolean
}

const signMap: Record<HostName, string> = {
    'sirius': '*#06#c2uXpo9IeKaIkpyJdXipfGxs',
    'misc': '*#06#j5moQpWNkIhrjaSFgodFh52T',
    'activity': '*#06#j5moQpWNkIhrjaSFgodFh52T',
    'config': '*#06#d3pycm9DSYd6lndDckVwkzyZ',
    'swallow': '*#06#j5moQpWNkIhrjaSFgodFh52T',
    'jiakao3d': '*#06#bIWiiqmXc3lspqhJj5NIipya',
    'squirrel': '*#06#iKVuc32KRW12cqg8QnGkdX16',
    'cheyouquan': '',
    'monkey': '*#06#PGuPbJiIkz2PeItsc5qKhItG',
    'jiakao': '',
    'tiku': '*#06#i4mleXFFkIlCqXWal3eCQkN6',
    'panda': '*#06#l5J2nW13l3qEfHdubKKmPJyj',
    'parrot': '*#06#c2uXpo9IeKaIkpyJdXipfGxs',
    'pony': '*#06#bJaWjoOnhaN9pXCCbqiSoqd2',
    'rights': '*#06#R31tjnFuiaJubpKojalHbnmG',
    'login': '',
    'apiShare': '',
};

const resignMap: Record<HostName, string> = {
    'sirius': 'hello',
    'misc': 'debug',
    'activity': 'hello',
    'config': '',
    'swallow': 'hello1',
    'jiakao3d': 'xx',
    'squirrel': 'helloworld',
    'cheyouquan': '',
    'monkey': 'debug',
    'jiakao': 'debug',
    'tiku': '_debug',
    'panda': 'debug',
    'pony': 'hello',
    'parrot': 'hello',
    'rights': 'hello',
    'login': '',
    'apiShare': '',
};

var authTokenFn = async function () {
    if (Platform.isMuCang) {
        return getAuthToken();
    }
    const authToken = JSON.parse(localStorage.getItem('mucang_userInfo') || '{}')?.authToken || '';
    return authToken
}
let isJumpLogin = false;
/** 发起网络请求 */
export async function request({ hostName = 'sirius', url, method = 'GET', headers, data = {}, others = {}, noToast, returnStore }: RequestOptions): Promise<any> {
    /*
     | 处理参数
     */
    let params: any = {};
    const authToken = await authTokenFn();

    if (authToken && !data.authToken) {
        data.authToken = authToken;
    }

    if (!Platform.isMuCang) {
        data = {
            ...(() => {
                const whiteUrlParams: Record<string, string> = {};
                for (const key in URLParams) {
                    if (!(/^_/).test(key)) {
                        whiteUrlParams[key] = URLParams[key];
                    }
                }
                return whiteUrlParams;
            })(),
            ...data,
            _r: sign(1),
            resign: resignMap[hostName]
        };
    }

    // 如果要用json形式发请求，参数需要JSON.stringify
    if (others?.ajaxOptions) {

        params = JSON.stringify(data);
    } else {
        params = data;
    }
    return new Promise((resovle, reject) => {
        (new (MCBaseStore.extend({
            clientVersion: 2048,
            url: `${hostName}://${url}`,
            sign: signMap[hostName],
            headers,
            method: method.toLocaleUpperCase(),
            ...others,
            errorToast: false,
            type: 'online'
        }))()).request(params).then((res: any, store: any) => {
            // console.info('[response]', url, res);
            resovle(returnStore ? store.data : res);
        }).fail((_errorCode: number, error: any) => {
            const text = JSON.parse(error.text);
            if (!noToast) {
                makeToast(error.statusText || '');
            }
            if (text.errorCode === 403 && !isJumpLogin) {
                isJumpLogin = true;
                if (localStorage.getItem('mucang_userInfo')) {
                    localStorage.removeItem('mucang_userInfo');
                    localStorage.removeItem('vip-active-authToken');
                    Jump.reload();
                }

            }
            reject(error);
        });
    });
}
