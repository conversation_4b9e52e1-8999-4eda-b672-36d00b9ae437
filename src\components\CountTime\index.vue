<script setup lang="ts">
import { getServerTime } from '@/api/chores';
import { jump } from '@jiakaobaodian/jiakaobaodian-utils';
import { defineProps, onMounted, ref } from 'vue';
const timeStr = ref('');
const Jump = jump.default;
let timer: ReturnType<typeof setInterval>;
const props = defineProps({
    // 服务器时间
    serverTime: Number,
    // 结束时间
    endTime: Number,
});
const getHMT = (leftTime: number) => {
    let day: any = Math.floor(leftTime / (60 * 60 * 1000 * 24));

    let hour: any = Math.floor(((leftTime - day * 60 * 60 * 1000 * 24) / (60 * 60 * 1000)) % 24);
    let min: any = Math.floor((leftTime - day * 60 * 60 * 1000 * 24 - hour * 60 * 60 * 1000) / (60 * 1000));
    let sec: any = Math.floor((leftTime - day * 60 * 60 * 1000 * 24 - hour * 60 * 60 * 1000 - min * 60 * 1000) / 1000);

    if (hour < 10) {
        hour = '0' + hour;
    }
    if (min < 10) {
        min = '0' + min;
    }
    if (sec < 10) {
        sec = '0' + sec;
    }

    if (day <= 0) {
        return hour + ':' + min + ':' + sec;
    } else if (day >= 3) {
        day += '天';
        return day + hour + ':' + min + ':' + sec;
    }
    return day * 24 + parseInt(hour) + ':' + min + ':' + sec;
};

const startCount = (leftTime: number) => {
    clearInterval(timer);

    if (leftTime > 0) {
        timer = window.setInterval(() => {
            if (leftTime > 0) {
                timeStr.value = getHMT(leftTime);
                leftTime -= 1000;
            } else {
                Jump.reload();
            }
        }, 1000);

        timeStr.value = getHMT(leftTime);
    }
};

onMounted(async () => {
    const { endTime } = props;
    let { serverTime } = props;

    if (!endTime) {
        console.error('倒计时组件参数错误');
        return;
    }
    
    if (!serverTime) {
        serverTime = await getServerTime();
    }

    let leftTime = endTime - serverTime;
   
    startCount(leftTime);
});
</script>

<template>
    <i class="count-content {{timeStr ? '': 'hide'}}">{{ timeStr }}</i>
</template>

<style lang="less" scoped>
/* @import './index.less'; */
</style>
