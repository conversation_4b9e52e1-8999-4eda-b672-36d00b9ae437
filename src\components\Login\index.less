.common-login {
    background-color: white;
    padding-top: 20px;

    >.sign-txt {
        padding-left: 15px;
        font-size: 30px;
        margin-bottom: 40px;
    }

    .line {
        margin-left: 20px;
        height: 70px;
        width: 335px;
        margin: 0 auto;
        position: relative;
    }

    .line input {
        font-size: 15px;
        height: 68px;
        border: none;
    }

    .line.b {
        border-bottom: 1px solid #eee;
    }

    .line .pwd {
        width: 225px;
        float: left;
    }

    .yzm {
        font-size: 13px;
        color: #3184ed;
        border: 1px solid #3184ed;
        border-radius: 100px;
        height: 30px;
        width: 97px !important;
        display: flex;
        justify-content: center;
        align-items: center;
        float: right;
        text-align: center;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: 0;
        font-weight: normal;
        background-color: #fff;
    }

    .yzm.disabled {
        color: #fff;
        background: #bababa;
        border-color: #bababa;
    }

    .desc {
        font-size: 13px;
        color: #999;
        margin: 12px 20px;
    }

    .btn {
        margin: 30px 20px 20px 20px;
        height: 50px;
        border: none;
        background-color: #008AFF;
        color: #fff;
        text-align: center;
        line-height: 50px !important;
        border-radius: 25px;
        font-weight: normal;
        font-size: 17px;
    }

    .buttom {
        font-size: 17px;
        color: #566899 !important;
        text-align: center;
        border: 0 !important;
        font-weight: normal;
    }

    .buttom image {
        width: 25px;
        height: 22px;
        position: relative;
        top: 5px;
        display: inline-block;
    }

    .ui-bg {
        position: fixed;
        left: 0;
        top: 0;
        bottom: 0;
        right: 0;
        background-color: rgba(255, 255, 255, 0.5);
    }

    .user-info {
        position: fixed;
        border: 1px solid #ddd;
        background-color: #fff;
        height: 100px;
        width: 80%;
        top: 50%;
        margin-top: -50px;
        left: 10%;
        z-index: 2;
    }

    .user-info button {
        margin-top: 25px;
    }


    .core--yidun-popup__close {
        height: 0 !important;
        text-align: right !important;
    }

    .inputs {
        background-color: #fff;
    }

    .privacy {
        font-size: 12px;
        margin-top: 20px;
        margin-bottom: 20px;
        padding: 0 15px;
        display: flex;
        align-items: center;
        .go-protocol {
            color: #36A2FF;
        }

        .check-box {
            width: 15px;
            height: 15px;
            display: inline-block;
            box-sizing: border-box;
            border-radius: 4px;
            margin-right: 5px;
            position: relative;
            background: url(https://web-resource.mucang.cn/minprogram/fenxiao/<EMAIL>) no-repeat center center/cover;
            vertical-align: top;

            .tip {
                position: absolute;
                left: 0;
                top: -10px;
                transform: translate(-5px, -100%);
                white-space: nowrap;
                background-color: rgba(0, 0, 0, 0.6);
                padding: 5px 10px;
                color: white;
                border-radius: 3px;

                &::after {
                    content: '';
                    position: absolute;
                    border-top: 8px solid rgba(0, 0, 0, 0.6);
                    border-right: 8px solid transparent;
                    border-left: 8px solid transparent;
                    bottom: 0;
                    left: 8px;
                    transform: translateY(100%);
                }
            }

            &.checked .tip {
                display: none;
            }

            &.checked:after {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                background: url(https://web-resource.mucang.cn/minprogram/fenxiao/jiakao_ic_login_yhxy1备份@2x.png) no-repeat center center/cover;
                transform: translate(-50%, -50%);
                width: 15px;
                height: 15px;
            }
        }
    }
}



.mark {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1100;
    overflow: hidden;
    display: none;

    &.active {
        display: block;
    }

    .captcha {
        width: 280px;
        height: 200px;
        background: #fff;
        position: absolute;
        left: 50%;
        top: 50%;
        margin-left: -140px;
        margin-top: -140px;
    }
}
