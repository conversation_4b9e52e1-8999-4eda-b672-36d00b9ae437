<script setup lang="ts">
import { ref, computed, h } from 'vue';
import Dialog from '@/components/Dialog/index.vue';
import ReadProtocol from '@/components/ReadProtocol/index.vue';
import { makeToast } from '../../common/utils';
import { request } from '../../common/request';
import { jump } from '@jiakaobaodian/jiakaobaodian-utils';
import { EnumKeys, ProtocolUrl } from '../../common/const';
import { codeCheck, sendLogin } from '../../api/login';
import { saveCache } from '../../common/core';
import { dialog } from '@/common/dom-function';
const Jump = jump.default;

const argeeProtocol = ref(false);
const isMark = ref(false);
const smsTime = ref(-1);
const phone = ref('');
const smsId = ref('');
const code = ref('');

const emit = defineEmits(['loginSuccess']);

const isDisabled = computed(() => {
    return smsTime.value >= 0;
});

const setCodeTimer = () => {
    smsTime.value = 60;
    const codeTimer = setInterval(() => {
        if (smsTime.value === 0) {
            clearInterval(codeTimer);
            smsTime.value = -1;
            return;
        }
        smsTime.value = smsTime.value - 1;
    }, 1000);
};

const onCodeCheck = (NECaptchaValidate: any) => {
    codeCheck({
        phoneNumber: phone.value,
        NECaptchaValidate: NECaptchaValidate,
    }).then((data) => {
        smsId.value = data.smsId;

        setCodeTimer();
    });
};

const captcha = () => {
    let captchaIns: {
        refresh: () => {};
    };

    isMark.value = true;

    window.initNECaptcha(
        {
            captchaId: '6f92317b6e7d4f4faa77a360d65826c5',
            element: '#captcha',
            mode: 'embed',
            onReady: function () {
                // 验证返回函数
            },
            onVerify: (err: any, data: any) => {
                if (!err) {
                    if (data && data.validate) {
                        onCodeCheck(data.validate);
                        setTimeout(() => {
                            isMark.value = false;
                        }, 500);
                    }
                } else {
                    setTimeout(function () {
                        captchaIns.refresh();
                    }, 500);
                }
            },
        },
        function onload(instance: any) {
            captchaIns = instance;
            // 初始化成功
        },
        function onerror() {
            // 验证码初始化失败处理逻辑，例如：提示用户点击按钮重新初始化
        }
    );
};

const onLogin = () => {
    new Promise<void>((resolve) => {
        if (argeeProtocol.value) {
            resolve();
        } else {
            dialog(
                Dialog,
                { cancleText: '不同意' },
                h(ReadProtocol, {
                    hideCheckbox: true,
                    protocolList: [
                        {
                            text: '隐私政策',
                            url: ProtocolUrl.YINSI,
                        },
                        {
                            text: '用户协议',
                            url: ProtocolUrl.USER_SERVER,
                        },
                    ],
                })
            ).then((flag) => {
                if (flag) {
                    resolve();
                }
            });
        }
    }).then(() => {
        argeeProtocol.value = true;
        if (!phone.value) {
            makeToast('请填写正确的手机号');
            return;
        }
        if (!smsId.value) {
            makeToast('请先发送验证码');
            return;
        }

        if (!code.value) {
            makeToast('请填写验证码');
            return;
        }

        sendLogin({
            phoneNumber: phone.value,
            smsCode: code.value,
            smsId: smsId.value,
        }).then((data) => {
            saveCache({
                key: 'mucang_userInfo',
                value: JSON.stringify(data),
            });

            saveCache({
                key: 'vip-active-authToken',
                value: data.authToken,
            });

            emit('loginSuccess', data);
        });
    });
};

const onGetCaptcha = () => {
    if (smsTime.value >= 0) {
        return;
    }
    if (!/^\d{11}$/.test(phone.value)) {
        makeToast('手机号码有误，请重填');
        return;
    }

    captcha();
};

const onChangeProtocol = (e: Event) => {
    e.preventDefault();
    argeeProtocol.value = !argeeProtocol.value;
};

const onGoProtocol = (protocolName: EnumKeys<typeof ProtocolUrl>) => {
    Jump.openWeb({
        url: ProtocolUrl[protocolName],
    });
};
</script>

<template>
    <div class="common-login">
        <div class="sign-txt">您好，请登录</div>
        <div class="privacy">
            <div @click="onChangeProtocol" class="check-box" :class="{ 'checked': argeeProtocol }">
                <div class="tip">请先勾选，同意后再进行登录</div>
            </div>
            <span @click="onChangeProtocol">已阅读并同意</span>
            <span @click="onGoProtocol('YINSI')" class="go-protocol">《隐私政策》</span>
            和
            <span @click="onGoProtocol('USER_SERVER')" class="go-protocol">《用户协议》</span>
        </div>
        <div class="inputs">
            <div class="line b">
                <input type="number" placeholder="请输入手机号" maxlength="11" placeholder-style="color: #BABABA;" v-model="phone" />
            </div>
            <div class="line b">
                <input type="text" class="pwd" v-model="code" placeholder="请输入验证码" placeholder-style="color: #BABABA;" />
                <div hover-class="none" disabled="{{isDisabled > 0}}" class="yzm" :class="{ 'disabled': isDisabled }" @click="onGetCaptcha">{{ isDisabled ? smsTime + '秒' : '获取验证码' }}</div>
            </div>
        </div>

        <div class="btn" @click="onLogin">登录</div>

        <div class="mark" :class="{ active: isMark }" ref="mark">
            <div class="captcha" id="captcha" ref="captha"></div>
            <!-- 验证码容器元素 -->
        </div>
    </div>
</template>

<style lang="less" scoped>
@import './index.less';
</style>
