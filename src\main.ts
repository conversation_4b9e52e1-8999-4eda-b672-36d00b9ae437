import Vconsole from "vconsole";
if (import.meta.env.MODE === "development") {
    // 测试和开发打开，生产不能打开
    new Vconsole();
}

import { createApp } from 'vue'
import './assets/init.less'
import './style.css'
import './rem.ts'
import '@/common/mcprotocol.ts'
import App from './App.vue'
import router from './router'
import pinia from './store'
import { MCBaseStore } from '@simplex/simple-base';

let host = {
    "sirius": "https://sirius.kakamobi.cn/",
    "misc": "https://jiakao-misc.kakamobi.cn/",
    "config": "https://config.kakamobi.com/",
    "activity": "https://jiakao-activity.kakamobi.cn/",
    "swallow": "https://swallow.kakamobi.cn/",
    "jiakao3d": "https://jiakao-3d.kakamobi.cn/",
    "squirrel": "https://squirrel.kakamobi.cn/",
    "cheyouquan": "https://cheyouquan.kakamobi.com/",
    "login": "https://auth.kakamobi.com",
    "panda": "https://panda.kakamobi.cn/",
    "apiShare": "https://api-share-m.kakamobi.com/",
    "chat": "https://chat-platform.mucang.cn/",
    "parrot": "https://parrot.kakamobi.cn/",
    "malibu": "https://malibu.kakamobi.cn/",
    "mona": "https://mona.kakamobi.cn/",
    "jiaxiaoAwd": "https://jiaxiao-awd.kakamobi.cn/",
    "pony": "https://pony.kakamobi.cn/",
};
let testHost = {
    "sirius": "https://sirius2.ttt.mucang.cn/",
    "misc": "https://jiakao-misc.ttt.mucang.cn/",
    "config": "https://config.ttt.mucang.cn/",
    "activity": "https://jiakao-activity.ttt.mucang.cn/",
    "swallow": "https://swallow.ttt.mucang.cn/",
    "jiakao3d": "https://jiakao-3d.ttt.mucang.cn/",
    "squirrel": "https://squirrel.ttt.mucang.cn/",
    "cheyouquan": "https://cheyouquan.kakamobi.com/",
    "login": "https://auth.kakamobi.com",
    "panda": "https://panda.ttt.mucang.cn/",
    "apiShare": "https://api-share-m.kakamobi.com/",
    "chat": "https://chat-platform.ttt.mucang.cn/",
    "parrot": "https://parrot.ttt.mucang.cn/",
    "malibu": "https://malibu.ttt.mucang.cn/",
    "mona": "https://mona.ttt.mucang.cn/",
    "jiaxiaoAwd": "https://jiaxiao-awd.ttt.mucang.cn/",
    "pony": "https://pony.ttt.mucang.cn/"
};

MCBaseStore.setHosts(import.meta.env.MODE === "development" ? testHost : host);

const app = createApp(App);
app.use(pinia)
app.use(router)
app.mount('#app')
