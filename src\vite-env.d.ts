/// <reference types="vite/client" />

declare module '*.vue' {
    import { DefineComponent } from "vue"
    const component: DefineComponent<{}, {}, any>
    export default component
}

declare namespace Package {
    const hosts: { [key: string]: string };
    const build: {
        style: {
            baseWidth: number
        }
    };
}

declare module '*.html' {
    const value: any;
    export default value;
}

declare module '*.less' {
    const value: any;
    export default value;
}

declare module '*.png' {
    const value: any;
    export default value;
}

interface Window {
    // 微信的SDK
    wx: any
    // 登录控件（网易云盾）
    initNECaptcha
    /*
    ｜ 购买相关回调
    */
    iosBuySuccess(groupKey): void;
    buySuccess(): void;
    buyCancel(): void;
    buyFailed(): void;

}

type AnyFunc = (...args: any[]) => any;
