import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import pxtorem from 'postcss-pxtorem'
import legacy from '@vitejs/plugin-legacy'
import path from 'path';

// https://vite.dev/config/
export default defineConfig({
  base: '/activity.kakamobi.com/jiakaobaodian-66-mona/',
  // base: '/dist/',
  plugins: [
    vue(),
    legacy({
      targets: ['chrome 61', 'iOS 11']
    })
  ],
  resolve: {
    alias: {
      "@": '/src'
    },
  },
  build: {
    // target: 'chrome61',
    // cssTarget: 'chrome61',
    // sourcemap: true,
    rollupOptions: {
      input: {
        vip: path.resolve(__dirname, './pages/vip.html'),
        // recharge: path.resolve(__dirname, './pages/recharge.html'),
        // rechargeList: path.resolve(__dirname, './pages/rechargeList.html'),
        // rechargeDetails: path.resolve(__dirname, './pages/rechargeDetails.html'),
        // rechargeDialog: path.resolve(__dirname, './pages/rechargeDialog.html'),
      }
    }
  },
  css: {
    postcss: {
      plugins: [
        pxtorem({
          rootValue: 100,
          propList: ['*'],
        }),
      ],
    },
  },
  server: {
    host: '0.0.0.0', // 或者你的局域网IP地址，例如：'*************'
  }
})
